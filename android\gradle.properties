org.gradle.jvmargs=-Xmx8G -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Fix for Windows paths with spaces
org.gradle.caching=true
org.gradle.parallel=true
org.gradle.configureondemand=true

# Disable Gradle daemon for better compatibility with spaces in paths
org.gradle.daemon=false

# Additional settings for Windows path compatibility
org.gradle.workers.max=1
android.builder.sdkDownload=true
